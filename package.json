{"name": "feedback", "description": "Feedback management system", "version": "4.0.0", "license": "MIT", "author": "Dynes", "main": "dist/js/adminlte.min.js", "scripts": {"dev": "npm-run-all --parallel watch docs-serve", "css": "npm-run-all css-compile css-prefix css-rtl css-minify", "css-compile": "sass --style expanded --load-path=\"node_modules\" --source-map --embed-sources --no-error-css src/scss/:dist/css/", "css-rtl": "cross-env NODE_ENV=RTL postcss --config src/config/postcss.config.mjs --dir \"dist/css\" --ext \".rtl.css\" \"dist/css/*.css\" \"!dist/css/*.min.css\" \"!dist/css/*.rtl.css\"", "css-lint": "stylelint \"src/scss/**/*.scss\" --cache --cache-location .cache/.stylelintcache --rd", "css-minify": "npm-run-all --aggregate-output --parallel css-minify-*", "css-minify-main": "cleancss -O1 --format breakWith=lf --with-rebase --source-map --source-map-inline-sources --output dist/css/ --batch --batch-suffix \".min\" \"dist/css/*.css\" \"!dist/css/*.min.css\" \"!dist/css/*rtl*.css\"", "css-minify-rtl": "cleancss -O1 --format breakWith=lf --with-rebase --source-map --source-map-inline-sources --output dist/css/ --batch --batch-suffix \".min\" \"dist/css/*rtl.css\" \"!dist/css/*.min.css\"", "css-prefix": "postcss --config src/config/postcss.config.mjs --replace \"dist/css/*.css\" \"!dist/css/*.rtl*.css\" \"!dist/css/*.min.css\"", "js": "npm-run-all js-compile js-minify", "js-compile": "rollup --config src/config/rollup.config.js --sourcemap", "js-lint": "eslint --cache --cache-location .cache/.eslintcache --report-unused-disable-directives .", "js-minify": "terser --compress passes=2 --mangle --comments \"/^!/\" --source-map \"content=dist/js/adminlte.js.map,includeSources,url=adminlte.min.js.map\" --output dist/js/adminlte.min.js dist/js/adminlte.js", "bundlewatch": "bundlewatch --config .bundlewatch.config.json", "lockfile-lint": "lockfile-lint --allowed-hosts npm --allowed-schemes https: --empty-hostname false --type npm --path package-lock.json", "docs-compile": "astro --config src/config/astro.config.mjs build", "docs-lint": "astro --config src/config/astro.config.mjs check", "docs-format": "prettier --write \"dist/pages/**/*.html\"", "docs-serve": "astro --config src/config/astro.config.mjs dev --open --port 3000", "assets": "node src/config/assets.config.mjs", "lint": "npm-run-all --aggregate-output --continue-on-error --parallel js-lint css-lint docs-lint lockfile-lint", "clean": "<PERSON><PERSON><PERSON> dist", "compile": "npm-run-all docs-compile docs-format assets css js", "production": "npm-run-all clean lint compile", "watch": "concurrently \"npm:watch-*\"", "watch-css-main": "nodemon --watch src/scss/ --ext scss --exec \"npm-run-all css-lint css-compile\"", "watch-css-dist": "nodemon --watch dist/css/ --ext css --ignore \"dist/css/*.rtl.*\" --exec \"npm run css-rtl\"", "watch-js-main": "nodemon --watch src/ts/ --ext ts --exec \"npm-run-all js-lint js-compile\"", "watch-assets": "nodemon --watch src/assets/ --exec \"npm run assets\""}, "keywords": ["css", "sass", "responsive", "admin", "template", "theme", "framework", "control-panel", "dashboard"], "homepage": "https://adminlte.io", "style": "dist/css/adminlte.css", "sass": "src/scss/adminlte.scss", "repository": {"type": "git", "url": "git://github.com/ColorlibHQ/AdminLTE.git"}, "bugs": {"url": "https://github.com/ColorlibHQ/AdminLTE/issues"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@astrojs/mdx": "^4.0.1", "@rollup/plugin-typescript": "^12.1.1", "@typescript-eslint/eslint-plugin": "^7.5.0", "@typescript-eslint/parser": "^7.5.0", "astro": "^5.0.3", "autoprefixer": "^10.4.20", "bootstrap": "^5.3.2", "bundlewatch": "^0.4.0", "clean-css-cli": "^5.6.3", "concurrently": "^9.1.0", "cross-env": "^7.0.3", "eslint": "^8.55.0", "eslint-config-xo": "^0.44.0", "eslint-config-xo-typescript": "^4.0.0", "eslint-plugin-astro": "^0.34.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-unicorn": "^52.0.0", "fs-extra": "^11.1.1", "lockfile-lint": "^4.14.0", "nodemon": "^3.1.4", "npm-run-all": "^4.1.5", "postcss": "^8.4.43", "postcss-cli": "^11.0.0", "prettier": "^3.3.3", "prettier-plugin-astro": "^0.14.1", "rimraf": "^6.0.1", "rollup": "^4.28.1", "rtlcss": "^4.3.0", "sass": "^1.78.0", "stylelint": "^16.11.0", "stylelint-config-twbs-bootstrap": "^15.1.0", "terser": "^5.31.6", "tslib": "^2.8.1", "typescript": "^5.6.3"}}